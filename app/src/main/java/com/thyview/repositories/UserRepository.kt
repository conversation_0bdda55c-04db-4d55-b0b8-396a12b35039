package com.thyview.repositories

import com.thyview.models.*
import com.thyview.services.UserApiService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository for user-related operations
 */
@Singleton
class UserRepository @Inject constructor(
    private val userApiService: UserApiService,
    private val okHttpClient: OkHttpClient
) {

    // Create a separate OkHttpClient without Firebase Auth interceptor for S3 uploads
    private val s3OkHttpClient = OkHttpClient.Builder()
        .connectTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .readTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .writeTimeout(60, java.util.concurrent.TimeUnit.SECONDS)
        .build()
    
    /**
     * Create a new user
     */
    suspend fun createUser(request: CreateUserRequest): Result<User> {
        return try {
            Timber.d("Creating new user with email: ${request.email}")
            val user = userApiService.createUser(request)
            Timber.d("User created successfully: ${user.id}, username: ${user.username}")
            Result.success(user)
        } catch (e: Exception) {
            val errorMsg = when {
                e.message?.contains("409") == true -> "Username already taken (409)"
                e.message?.contains("403") == true -> "Access forbidden (403) - check auth token"
                e.message?.contains("401") == true -> "Unauthorized (401) - invalid auth token"
                else -> "API error: ${e.message}"
            }
            Timber.e(e, "Failed to create user: $errorMsg")
            Result.failure(Exception(errorMsg, e))
        }
    }
    
    /**
     * Get current authenticated user
     */
    suspend fun getCurrentUser(): Result<User> {
        return try {
            Timber.d("Fetching current user from API...")
            val user = userApiService.getCurrentUser()
            Timber.d("Retrieved current user: ${user.id}, username: ${user.username}")
            Result.success(user)
        } catch (e: Exception) {
            val errorMsg = when {
                e.message?.contains("404") == true -> "User not found (404)"
                e.message?.contains("403") == true -> "Access forbidden (403) - check auth token"
                e.message?.contains("401") == true -> "Unauthorized (401) - invalid auth token"
                else -> "API error: ${e.message}"
            }
            Timber.e(e, "Failed to get current user: $errorMsg")
            Result.failure(Exception(errorMsg, e))
        }
    }
    
    /**
     * Get user by external ID
     */
    suspend fun getUserByExternalId(externalUserId: String): Result<User> {
        return try {
            val user = userApiService.getUserByExternalId(externalUserId)
            Timber.d("Retrieved user by external ID: ${user.id}")
            Result.success(user)
        } catch (e: Exception) {
            Timber.e(e, "Failed to get user by external ID: $externalUserId")
            Result.failure(e)
        }
    }
    
    /**
     * Update current user profile
     */
    suspend fun updateUser(request: UpdateUserRequest): Result<User> {
        return try {
            Timber.d("Updating user with request: username='${request.username}', languages=${request.preferredLanguages}")
            val user = userApiService.updateUser(request)
            Timber.d("User updated successfully: ${user.id}, username='${user.username}'")
            Result.success(user)
        } catch (e: Exception) {
            Timber.e(e, "Failed to update user")
            Result.failure(e)
        }
    }
    
    /**
     * Delete current user
     */
    suspend fun deleteUser(): Result<DeleteUserResponse> {
        return try {
            val response = userApiService.deleteUser()
            Timber.d("User deleted successfully")
            Result.success(response)
        } catch (e: Exception) {
            Timber.e(e, "Failed to delete user")
            Result.failure(e)
        }
    }
    
    /**
     * Check username availability
     */
    suspend fun checkUsernameAvailability(username: String): Result<Boolean> {
        return try {
            val response = userApiService.checkUsernameAvailability(username)
            Timber.d("Username availability check for '$username': ${response.available}")
            Result.success(response.available)
        } catch (e: Exception) {
            Timber.e(e, "Failed to check username availability for: $username")
            Result.failure(e)
        }
    }

    /**
     * Get presigned URL for profile image upload
     */
    suspend fun getProfileImagePresignedUrl(): Result<ProfileImagePresignResponse> {
        return try {
            Timber.d("Getting presigned URL for profile image upload")
            val response = userApiService.getProfileImagePresignedUrl()
            Timber.d("Got presigned URL: ${response.url}")
            Result.success(response)
        } catch (e: Exception) {
            val errorMsg = when {
                e.message?.contains("403") == true -> "Access forbidden (403) - check auth token"
                e.message?.contains("401") == true -> "Unauthorized (401) - invalid auth token"
                else -> "API error: ${e.message}"
            }
            Timber.e(e, "Failed to get presigned URL: $errorMsg")
            Result.failure(Exception(errorMsg, e))
        }
    }

    /**
     * Upload image to S3 using presigned URL
     */
    suspend fun uploadImageToS3(presignedUrl: String, imageFile: File): Result<Unit> {
        return withContext(Dispatchers.IO) {
            try {
                Timber.d("Uploading image to S3: ${imageFile.name}")

                val mediaType = "image/jpeg".toMediaType()
                val requestBody = imageFile.readBytes().toRequestBody(mediaType)

                val request = Request.Builder()
                    .url(presignedUrl)
                    .put(requestBody)
                    .build()

                val response = s3OkHttpClient.newCall(request).execute()

                if (response.isSuccessful) {
                    Timber.d("Image uploaded successfully to S3")
                    Result.success(Unit)
                } else {
                    val errorMsg = "S3 upload failed: ${response.code} ${response.message}"
                    Timber.e(errorMsg)
                    Result.failure(Exception(errorMsg))
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to upload image to S3")
                Result.failure(e)
            }
        }
    }

    /**
     * Get current user's profile image URL
     */
    suspend fun getProfileImageUrl(): Result<String?> {
        return try {
            Timber.d("Getting profile image URL")
            val response = userApiService.getProfileImageUrl()
            Timber.d("Got profile image URL: ${response.url}")
            Result.success(response.url)
        } catch (e: Exception) {
            val errorMsg = when {
                e.message?.contains("404") == true -> "Profile image not found (404)"
                e.message?.contains("403") == true -> "Access forbidden (403) - check auth token"
                e.message?.contains("401") == true -> "Unauthorized (401) - invalid auth token"
                else -> "API error: ${e.message}"
            }
            Timber.e(e, "Failed to get profile image URL: $errorMsg")
            Result.failure(Exception(errorMsg, e))
        }
    }
}
