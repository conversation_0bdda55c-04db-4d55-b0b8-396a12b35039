package com.thyview.models

import com.google.gson.annotations.SerializedName

/**
 * User role enum
 */
enum class UserRole {
    @SerializedName("user")
    USER,
    @SerializedName("admin")
    ADMIN
}

/**
 * User data model representing the API response
 */
data class User(
    @SerializedName("id") val id: String,
    @SerializedName("email") val email: String,
    @SerializedName("name") val name: String,
    @SerializedName("username") val username: String?,
    @SerializedName("bio") val bio: String?,
    @SerializedName("region") val region: String,
    @SerializedName("role") val role: UserRole,
    @SerializedName("externalUserId") val externalUserId: String,
    @SerializedName("preferredLanguages") val preferredLanguages: List<String>,
    @SerializedName("favoriteGenres") val favoriteGenres: List<String>,
    @SerializedName("isPrivate") val isPrivate: <PERSON><PERSON><PERSON>,
    @SerializedName("fans") val fans: Int,
    @SerializedName("fanned") val fanned: Int,
    @SerializedName("reviewCount") val reviewCount: Int,
    @SerializedName("watchListCount") val watchListCount: Int,
    @SerializedName("averageRating") val averageRating: Double,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("updatedAt") val updatedAt: String
)

/**
 * Request model for creating a new user
 */
data class CreateUserRequest(
    @SerializedName("email") val email: String,
    @SerializedName("name") val name: String,
    @SerializedName("profileImageUrl") val profileImageUrl: String?,
    @SerializedName("region") val region: String
)

/**
 * Request model for updating user profile
 */
data class UpdateUserRequest(
    @SerializedName("email") val email: String? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("username") val username: String? = null,
    @SerializedName("profileImageUrl") val profileImageUrl: String? = null,
    @SerializedName("bio") val bio: String? = null,
    @SerializedName("preferredLanguages") val preferredLanguages: List<String>? = null,
    @SerializedName("favoriteGenres") val favoriteGenres: List<String>? = null,
    @SerializedName("region") val region: String? = null
)

/**
 * Response model for username availability check
 */
data class UsernameAvailabilityResponse(
    @SerializedName("available") val available: Boolean
)

/**
 * Response model for user deletion
 */
data class DeleteUserResponse(
    @SerializedName("message") val message: String
)

/**
 * Response model for profile image presigned URL
 */
data class ProfileImagePresignResponse(
    @SerializedName("url") val url: String,
    @SerializedName("key") val key: String
)

/**
 * Response model for profile image URL
 */
data class ProfileImageUrlResponse(
    @SerializedName("url") val url: String?
)

/**
 * Error response model
 */
data class UserErrorResponse(
    @SerializedName("error") val error: String
)

/**
 * Supported languages for the app
 */
object SupportedLanguages {
    val languages = listOf(
        "Telugu",
        "Tamil", 
        "Hindi",
        "Malayalam",
        "Kannada",
        "Marathi",
        "Bengali",
        "Punjabi",
        "Gujarati",
        "Odia",
        "Bhojpuri",
        "Assamese",
        "English",
        "Korean",
        "Japanese",
        "Spanish",
        "French",
        "German",
        "Chinese",
        "Turkish",
        "Others"
    )
}
